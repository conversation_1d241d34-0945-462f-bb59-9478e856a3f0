@echo off
REM Claude Code Direct Swarm Invocation Helper (Windows)
REM Generated by ruv-swarm --claude setup

echo 🐝 ruv-swarm Claude Code Direct Invocation
echo ============================================
echo.

if "%1"=="research" (
    echo 🚀 Invoking Claude Code with research swarm...
    if "%3"=="true" (
        claude "Initialize a research swarm with 5 agents using ruv-swarm. Create researcher, analyst, and coder agents. Then orchestrate the task: %2" --dangerously-skip-permissions
    ) else (
        claude "Initialize a research swarm with 5 agents using ruv-swarm. Create researcher, analyst, and coder agents. Then orchestrate the task: %2"
    )
) else if "%1"=="development" (
    echo 🚀 Invoking Claude Code with development swarm...
    if "%3"=="true" (
        claude "Initialize a development swarm with 8 agents using ruv-swarm in hierarchical topology. Create architect, frontend coder, backend coder, and tester agents. Then orchestrate the task: %2" --dangerously-skip-permissions
    ) else (
        claude "Initialize a development swarm with 8 agents using ruv-swarm in hierarchical topology. Create architect, frontend coder, backend coder, and tester agents. Then orchestrate the task: %2"
    )
) else if "%1"=="custom" (
    echo 🚀 Invoking Claude Code with custom prompt...
    if "%3"=="true" (
        claude "%2" --dangerously-skip-permissions
    ) else (
        claude "%2"
    )
) else if "%1"=="help" (
    echo Usage:
    echo   claude-swarm.bat research "task description" [skip-permissions]
    echo   claude-swarm.bat development "task description" [skip-permissions]
    echo   claude-swarm.bat custom "full claude prompt" [skip-permissions]
    echo.
    echo Examples:
    echo   claude-swarm.bat research "Analyze modern web frameworks" true
    echo   claude-swarm.bat development "Build user authentication API"
    echo.
    echo Note: Add 'true' as the last parameter to use --dangerously-skip-permissions
) else (
    echo Unknown command: %1
    echo Run 'claude-swarm.bat help' for usage information
    exit /b 1
)